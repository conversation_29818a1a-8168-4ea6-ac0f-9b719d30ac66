package com.bilibili.miniapp.open.service.biz.income.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenIaaIncomeDetailDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPoExample;
import com.bilibili.miniapp.open.service.aspect.LockRequest;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.bo.income.*;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 收入服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class IncomeService implements IIncomeService {


    @Autowired
    private IAccountService accountService;

    @Autowired
    private IMiniAppService miniAppService;

    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Autowired
    private MiniAppOpenIaaIncomeDetailDao iaaIncomeDetailDao;

    @Override
    public IncomeSummaryBo getIncomeSummary(Long mid) {

        AssertUtil.isTrue(accountService.isCompanyOwner(mid), ErrorCodeType.NO_PERMISSION);

        List<MiniAppDTO> miniApps = miniAppRemoteService.queryMainMiniAppsFromCache(mid);

        if (CollectionUtils.isEmpty(miniApps)) {
            return IncomeSummaryBo.builder()
                    .withdrawableAmount(BigDecimal.ZERO)
                    .withdrawingAmount(BigDecimal.ZERO)
                    .build();
        }

        List<String> appIds = miniApps.stream().map(MiniAppDTO::getAppId).collect(Collectors.toList());

        return calculateIncomeSummary(appIds);
    }

    private IncomeSummaryBo calculateIncomeSummary(List<String> appIds) {

        MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
        example.createCriteria()
                .andAppIdIn(appIds)
                .andWithdrawStatusIn(List.of(WithdrawStatus.WITHDRAWABLE.getCode(), WithdrawStatus.WITHDRAWING.getCode()))
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenIaaIncomeDetailPo> incomeDetails = iaaIncomeDetailDao.selectByExample(example);

        if (CollectionUtils.isEmpty(incomeDetails)) {
            return IncomeSummaryBo.builder()
                    .withdrawableAmount(BigDecimal.ZERO)
                    .withdrawingAmount(BigDecimal.ZERO)
                    .build();
        }

        Map<Integer, List<MiniAppOpenIaaIncomeDetailPo>> statusMap = incomeDetails.stream()
                .collect(Collectors.groupingBy(MiniAppOpenIaaIncomeDetailPo::getWithdrawStatus));

        BigDecimal withdrawableAmount = calculateAmount(statusMap.get(WithdrawStatus.WITHDRAWABLE.getCode()));
        BigDecimal withdrawingAmount = calculateAmount(statusMap.get(WithdrawStatus.WITHDRAWING.getCode()));

        return IncomeSummaryBo.builder()
                .withdrawableAmount(withdrawableAmount)
                .withdrawingAmount(withdrawingAmount)
                .build();
    }

    private BigDecimal calculateAmount(List<MiniAppOpenIaaIncomeDetailPo> incomeDetailPos) {
        if (CollectionUtils.isEmpty(incomeDetailPos)) {
            return BigDecimal.ZERO;
        }
        return incomeDetailPos.stream()
                .map(MiniAppOpenIaaIncomeDetailPo::getActualIncomeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public IncomeDetailBo getIncomeDetails(Long mid, IncomeDetailQueryBo request) {

        validatePermission(mid, request.getAppId());

        List<MiniAppDTO> mainApps = miniAppRemoteService.queryMainMiniApps(mid);
        if (CollectionUtils.isEmpty(mainApps)) {
            return IncomeDetailBo.emptyInstance();
        }

        List<String> targetAppId = filterAppIdsByNameIfNecessary(mainApps, request.getAppName());

        MiniAppOpenIaaIncomeDetailPoExample example = buildQueryExample(targetAppId, request);

        long total = iaaIncomeDetailDao.countByExample(example);
        if (total == 0) {
            return IncomeDetailBo.emptyInstance();
        }

        Page page = Page.valueOf(request.getPage(), request.getSize());
        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());
        example.setOrderByClause("id desc");

        List<MiniAppOpenIaaIncomeDetailPo> incomeDetails = iaaIncomeDetailDao.selectByExample(example);

        Map<String, String> appNameMap = mainApps.stream()
                .collect(Collectors.toMap(MiniAppDTO::getAppId, MiniAppDTO::getName));

        List<IncomeDetailItemBo> records = incomeDetails.stream()
                .map(po -> convertToIncomeDetailItemBo(po, appNameMap))
                .collect(Collectors.toList());

        return IncomeDetailBo.builder()
                .records(records)
                .total(total)
                .build();
    }

    private List<String> filterAppIdsByNameIfNecessary(List<MiniAppDTO> mainApps, String fuzzyAppName) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isBlank(fuzzyAppName)) {
            return result;
        }
        return mainApps.stream()
                .filter(app -> app.getName().contains(fuzzyAppName))
                .map(MiniAppDTO::getAppId)
                .collect(Collectors.toList());
    }

    private void validatePermission(long mid, String appId) {
        if (StringUtils.isNotBlank(appId)) {
            AssertUtil.isTrue(accountService.isCompanyOwner(mid), ErrorCodeType.NO_PERMISSION);
        }
        AssertUtil.isTrue(accountService.hasAnyPermission(mid, appId), ErrorCodeType.NO_PERMISSION);
    }

    private MiniAppOpenIaaIncomeDetailPoExample buildQueryExample(List<String> targetAppIds, IncomeDetailQueryBo request) {
        MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
        MiniAppOpenIaaIncomeDetailPoExample.Criteria criteria = example.createCriteria();

        criteria.andIsDeletedEqualTo(0);

        if (!CollectionUtils.isEmpty(targetAppIds)) {
            criteria.andAppIdIn(targetAppIds);
        }

        if (StringUtils.isNotBlank(request.getBeginTime())) {
            criteria.andLogDateGreaterThanOrEqualTo(request.getBeginTime());
        }
        if (StringUtils.isNotBlank(request.getEndTime())) {
            criteria.andLogDateLessThanOrEqualTo(request.getEndTime());
        }

        if (request.getTrafficType() != null) {
            criteria.andTrafficTypeEqualTo(request.getTrafficType());
        }

        if (request.getWithdrawStatus() != null) {
            criteria.andWithdrawStatusEqualTo(request.getWithdrawStatus());
        }

        return example;
    }

    private IncomeDetailItemBo convertToIncomeDetailItemBo(MiniAppOpenIaaIncomeDetailPo po, Map<String, String> appNameMap) {
        IncomeDetailExtra incomeDetailExtra = JSON.parseObject(po.getExtra(), IncomeDetailExtra.class);
        return IncomeDetailItemBo.builder()
                .appId(po.getAppId())
                .appName(appNameMap.getOrDefault(po.getAppId(), po.getAppId()))
                .incomeTime(po.getLogDate())
                .trafficType(po.getTrafficType())
                .incomeAmount(po.getIncomeAmount())
                .channelFee(po.getChannelFee())
                .distributableIncomeAmount(po.getDistributableIncomeAmount())
                .actualIncomeAmount(po.getActualIncomeAmount())
                .withdrawStatus(po.getWithdrawStatus())
                .channelFeeRatio("--")
                .distributableRatio("--")
                .build();
    }

    @Override
    @LockRequest(key = "'process_daily_income' + #logDate")
    public void processDailyIncomeDetails(String logDate) {
        
    }
}
