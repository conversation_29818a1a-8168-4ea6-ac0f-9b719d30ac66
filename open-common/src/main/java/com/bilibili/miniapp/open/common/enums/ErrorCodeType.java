package com.bilibili.miniapp.open.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Getter
public enum ErrorCodeType {
    SUCCESS(0, "success"),
    //系统类：100-199
    SYSTEM_BUSY(100, "系统繁忙，请稍后再试"),//限频或者机器本身遇到了瓶颈
    SYSTEM_ERROR(102, "系统错误"),
    BAD_REQUEST(103, "请求失败"),
    BAD_PARAMETER(104, "参数异常"),
    UNAUTHORIZED(105, "unauthorized"),
    BAD_SIGN(106, "invalid sign"),
    PARSE_SIGN_PARAMETER_FAILED(107, "解析签名参数失败"),
    CONCURRENT_OPERATE(108, "当前操作繁忙，请勿并发操作"),
    FAILED_AND_RETRY_WAIT_MOMENT(109, "当前操作失败，但可稍后重试"),
    REQUEST_LIMIT(110, "请求频率过高，请稍后再试"),
    //业务类：200+
    NO_DATA(200, "数据不存在"),
    BAD_DATA(201, "非法数据"),
    EXISTS_DATA(202, "数据已存在"),
    NOT_ADMIN(203, "无超管权限"),
    NO_PERMISSION(204, "无权限"),


    NO_BIND_TEL(401, "未绑定手机号"),
    PRE_AUTH_CODE_EXPIRED(402, "凭证已失效"),
    PRE_AUTH_CODE_USED(403, "凭证已被使用"),
    ECPM_NO_DATA_OR_PAGE_ERROR(404, "请按顺序查询或已无更多数据"),

    //grpc
    WITHDRAW_AMOUNT_ERROR(600, "开具的发票金额和结算金额不同，请重新上传发票"),
    ;

    private final String message;

    private final Integer code;

    ErrorCodeType(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

}
